import { inject } from '@angular/core';
import { CanActivateFn, Router, UrlTree } from '@angular/router';
import { FirebaseAuthService } from '../services/firebase-auth.service';
import { StorageService } from '../services/storage.service';
import { map, take, switchMap, catchError } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { User } from 'firebase/auth';

/**
 * Auth guard to protect routes that require authentication
 * Redirects to login page if user is not authenticated
 */
export const authGuard: CanActivateFn = (route, state): Observable<boolean | UrlTree> => {
  const router = inject(Router);
  const authService = inject(FirebaseAuthService);
  const storageService = inject(StorageService);
  
  // First check Firebase auth state
  return authService.authState$.pipe(
    take(1),
    switchMap((user: User | null) => {
      if (user) {
        console.log('Auth Guard: User authenticated via Firebase');
        return of(true);
      }
      
      // If no Firebase user, check for stored token
      const token = storageService.getToken();
      if (token) {
        console.log('Auth Guard: User authenticated via stored token');
        return of(true);
      }
      
      // No authentication found, redirect to login
      console.log('Auth Guard: Access denied - Not authenticated');
      return of(router.createUrlTree(['/login']));
    }),
    catchError(error => {
      console.error('Auth Guard Error:', error);
      return of(router.createUrlTree(['/login']));
    })
  );
};
