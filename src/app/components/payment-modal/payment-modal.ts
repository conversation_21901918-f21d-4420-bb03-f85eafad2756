import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, OnChanges, ChangeDetectorRef } from '@angular/core';
import { PaymentService, PaymentMethod, PaymentData, PaymentModalOutput } from '../../services/payment.config.service';
import { ConfirmationService } from 'primeng/api';
import { CustomerService } from '../../services/customer.service';
import { CommonService } from 'src/app/services/common.service';
import { TypeSenseService } from 'src/app/services/typesense.service';

@Component({
  selector: 'app-payment-modal',
  standalone: false,
  templateUrl:'./payment-modal.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PaymentModalComponent implements OnChanges {
  @Input() visible = false;
  @Input() totalAmount = 0;
  @Input() isProcessing = false;
  @Input() title = 'Complete Payment';
  @Input() confirmButtonLabel = 'Confirm Payment';
  @Input() cancelButtonLabel = 'Cancel';
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() cancel = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<PaymentModalOutput>();
  paymentData: PaymentData = { selectedPaymentMethod: 'cash' };
  remainingAmount = 0;
  change = 0;
  isPaymentValid = false;
  paymentMethods: PaymentMethod[] = [];
  cashPaymentCompleted = false;
  cashAmountPaid = 0;
  showOnlinePaymentStep = false;
  customerDetails = { name: '', phone: '', create: false, customer_id: ''};
  constructor(
    private paymentService: PaymentService,
    private confirmationService: ConfirmationService,
    private customerService: CustomerService,
    private cdr: ChangeDetectorRef,
    private commonService: CommonService,
    private typeSenseService: TypeSenseService
  ) { }

  ionViewDidEnter(): void {
    this.customerDetails = { name: '', phone: '', create: false, customer_id: '' };
  }
  ngOnChanges(): void {
    if (this.totalAmount !== undefined) {
      this.updateAmounts();
      this.paymentMethods = this.paymentService.getPaymentMethods(this.totalAmount);
    }
    if (this.visible) this.resetPaymentData();
  }

  trackByMethod(_: number, m: PaymentMethod): string { return m.value; }

  getSelectedMethod(): PaymentMethod | undefined {
    return this.paymentMethods.find(m => m.value === this.paymentData.selectedPaymentMethod);
  }

  private resetPaymentData(): void {
    this.paymentData = this.paymentService.initializePaymentData(this.totalAmount);
    Object.assign(this, { customerDetails: { name: '', phone: '', create: false, customer_id: '' }, cashPaymentCompleted: false, cashAmountPaid: 0, showOnlinePaymentStep: false });
      this.updateAmounts();
  }

  updateAmounts(): void {
    const baseAmount = this.showOnlinePaymentStep ? this.totalAmount - this.cashAmountPaid : this.totalAmount;
    const { remaining, change, isValid } = this.paymentService.calculateAmounts(this.paymentData, baseAmount);
    this.remainingAmount = remaining;
    this.change = change;
    this.isPaymentValid = isValid;
    
    // Update payment methods to disable empty fields when total is reached
    this.paymentMethods = this.paymentMethods.map(method => {
      const amountKey = `${method.value}Amount` as keyof PaymentData;
      const amount = this.paymentData[amountKey] || 0;
      
      return {
        ...method,
        disabled: isValid && amount === 0 // Disable if amount is 0 when total is reached
      };
    });
    
    this.cdr.detectChanges();
  }

  handlePaymentMethodChange(method: string): void {
    if (!this.isProcessing) {
      this.paymentData.selectedPaymentMethod = method;
      this.updateAmounts();
    }
  }

  onAmountInput(event: Event, fieldKey: string): void {
    const input = event.target as HTMLInputElement;
    let value = parseFloat(input.value) || 0;
    const previousValue = this.paymentData[fieldKey] || 0;
    
    // If this is a cash payment and no other payment methods have values, allow any amount
    if (fieldKey === 'cashAmount' && 
        (!this.paymentData.onlineAmount || this.paymentData.onlineAmount <= 0) && 
        (!this.paymentData.walletAmount || this.paymentData.walletAmount <= 0)) {
      // No need to validate max for cash when other methods are empty
    } else {
      // For other cases, calculate total of other payment methods
      const otherPaymentsTotal = 
        (fieldKey !== 'onlineAmount' ? (this.paymentData.onlineAmount || 0) : 0) +
        (fieldKey !== 'walletAmount' ? (this.paymentData.walletAmount || 0) : 0) +
        (fieldKey !== 'cashAmount' ? (this.paymentData.cashAmount || 0) : 0);
      
      // Calculate remaining amount after other payments
      const remainingAfterOtherPayments = Math.max(0, this.totalAmount - otherPaymentsTotal);
      
      // Cap the value at the remaining amount after other payments
      if (value > remainingAfterOtherPayments) {
        value = remainingAfterOtherPayments;
        input.value = value.toString();
      }
    }
    
    // Update the payment data with the new value
    this.paymentData[fieldKey] = value;
    
    // If this is a cash payment and we're reducing the amount, clear other payment methods
    if (fieldKey === 'cashAmount' && value < previousValue) {
      this.clearOtherPaymentAmounts('cash');
    }
    
    this.updateAmounts();
  }

  onConfirm(): void {
    if (this.isProcessing) return;
    if (this.paymentData.selectedPaymentMethod === 'cash' && this.remainingAmount > 0) {
      this.proceedToCashPayment();
      return;
    }
    const validationAmount = this.getValidationAmount();
    const isSecondStep = this.showOnlinePaymentStep;
    
    if (!this.isPaymentValid) {
      this.commonService.toast({ 
        severity: 'error', 
        summary: 'Error', 
        detail: `Total payment amount must be exactly ₹${validationAmount.toFixed(2)}` 
      });
      return;
    }

    if (this.paymentService.validatePayment(this.paymentData, this.customerDetails.name, this.customerDetails.phone, validationAmount, isSecondStep)) {
      const output: PaymentModalOutput = {
        paymentMethod: this.getPaymentMethodForOutput(),
        paymentData: this.paymentData,
        customerName: this.customerDetails.name,
        customerPhone: this.customerDetails.phone,
        customerId: this.customerDetails.customer_id
      };
      
      if (this.cashPaymentCompleted) {
        output.multiPaymentData = {
          cashAmount: this.cashAmountPaid,
          onlineAmount: this.paymentData.onlineAmount || 0,
          walletAmount: this.paymentData.walletAmount || 0
        };
      }
      
      this.confirm.emit(output);
    }
  }

  onCancel(): void {
    if (!this.isProcessing) {
      this.visible = false;
      this.visibleChange.emit(false);
      this.cancel.emit();
    }
  }

  private proceedToCashPayment(): void {
    this.cashAmountPaid = this.paymentData.cashAmount || 0;
    this.cashPaymentCompleted = true;
    this.showOnlinePaymentStep = true;
    this.switchToPaymentMethod('cash');
  }

  private getValidationAmount(): number {
    return this.showOnlinePaymentStep ? this.totalAmount - this.cashAmountPaid : this.totalAmount;
  }

  private getPaymentMethodForOutput(): string {
    return this.cashPaymentCompleted ? 'mixed' : this.paymentData.selectedPaymentMethod;
  }

  switchToPaymentMethod(method:'cash' | 'online'| 'wallet'): void {
    this.paymentData.selectedPaymentMethod = method;
    this.paymentData[`${method}Amount`] = this.totalAmount - this.cashAmountPaid;
    this.clearOtherPaymentAmounts(method);
    this.updateAmounts();
  }

  clearOtherPaymentAmounts(excludeMethod: string): void {
    ['cashAmount', 'onlineAmount', 'walletAmount'].forEach(field => {
      if (field !== `${excludeMethod}Amount`) {
        this.paymentData[field as keyof PaymentData] = 0;
      }
    });
  }

  cancelPartialPayment(): void {
    this.confirmationService.confirm({
      message: `Are you sure you want to cancel the partial payment? This will reset to cash payment of ₹${this.totalAmount.toFixed(2)}.`,
      header: 'Cancel Partial Payment',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => this.resetToCashPayment()
    });
  }

  private resetToCashPayment(): void {
    Object.assign(this, { cashPaymentCompleted: false, cashAmountPaid: 0, showOnlinePaymentStep: false });
    Object.assign(this.paymentData, { selectedPaymentMethod: 'cash', cashAmount: this.totalAmount, upiId: '', cardType: undefined });
    this.clearOtherPaymentAmounts('cash');
    this.updateAmounts();
  }

  getConfirmButtonLabel(): string {
    return this.paymentData.selectedPaymentMethod === 'cash' && this.remainingAmount > 0 && !this.showOnlinePaymentStep
      ? 'Proceed to Online Payment' : this.confirmButtonLabel;
  }

  validatePhone(ev: any) {
    if (ev.target.value.length === 10) {
      this.customerDetails.phone = ev.target.value;
      this.customerService.verifyCustomer(ev.target.value).then((res: any) => {
        if (res?.data?.customer_name) {
          this.customerDetails.name = res.data.customer_name;
          this.customerDetails.customer_id = res.data.customer_id;
        } else {
          this.customerDetails.name = '';
          this.customerDetails.customer_id = '';
          this.customerDetails.create = true;
        }
        this.cdr.detectChanges();
      });
    } else {
      this.customerDetails.name = '';
      this.customerDetails.customer_id = '';
      this.customerDetails.create = false;
      this.cdr.detectChanges();
    }
  }
  createCustomer() {
    if (this.customerDetails.phone && this.customerDetails.name) {
      this.customerService.createCustomer({phone_number: this.customerDetails.phone, username: this.customerDetails.name}).then((res: any) => {
        this.commonService.setDisableBtn()
        if (res.success) {
          this.customerDetails.customer_id = res.data.customer_id;
          this.cdr.detectChanges();
        } else {
          this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Failed to create customer' });
        }
      });
    } else {
      this.commonService.setDisableBtn()
      this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Please enter phone number and name' });
    }
  }
}