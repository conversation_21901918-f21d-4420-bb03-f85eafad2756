<p-dialog appendTo="body" [modal]="true" [(visible)]="visible" [style]="{width: '50vw', 'max-width':'800px'}"
  [breakpoints]="{'960px': '75vw', '640px': '95vw'}" [closable]="false" [draggable]="false" [resizable]="false">
  <ng-template pTemplate="header">
    <div class="flex justify-between w-full">
      <h5>{{title}}</h5>
    </div>
  </ng-template>
  <div class="flex flex-col text-md">
    <!-- Customer Information -->
    <div class="w-full mb-2 mt-2">
      <h6 class="mb-2 leading-none font-semibold text-gray-800">Customer Information</h6>
      <div class="flex gap-4 mt-3">
        <input pInputText id="customerPhone" placeholder="Phone Number" [(ngModel)]="customerDetails.phone"
          [disabled]="isProcessing" autocomplete="off" class="w-full cutom-input" minlength="10" maxlength="10"
          type="tel" (keyup)="validatePhone($event)" />
        <input pInputText id="customerName" placeholder="Customer Name" [(ngModel)]="customerDetails.name"
          [disabled]="isProcessing" autocomplete="off" class="w-full cutom-input" />
        <button [appDisableDoubleClick]="true" class="w-[100px]" *ngIf="customerDetails.create" pButton type="button" label="Create"
          (click)="createCustomer()" [disabled]="isProcessing" size="small"></button>
      </div>
    </div>
    <!-- Payment Methods -->
    <div class="w-full mt-2">
      <h6 class="mb-3 leading-none font-semibold text-gray-800">Payment Methods</h6>
      <div class="space-y-4 mb-6">
        <div *ngFor="let method of paymentMethods; trackBy: trackByMethod" class="flex items-center gap-4 p-3 border rounded-lg">
          <!-- Payment Method Selector -->
          <div 
            class="flex items-center justify-between w-1/3 p-2 transition-all duration-200 border-2 rounded-lg cursor-pointer hover:shadow-md"
           >
            <div class="flex items-center">
              <i [class]="method.icon + ' text-lg mr-2'"></i>
              <span class="text-sm font-medium">
                {{method.label}}
              </span>
            </div>
          </div>
          
          <!-- Amount Input -->
          <div class="flex-1">
            <div class="flex items-center gap-2">
              <span class="text-sm font-medium text-gray-700 whitespace-nowrap">Amount ({{method.label}}):</span>
              <input 
                type="number" 
                pInputText 
                [value]="method.value === 'wallet' ? '' : (paymentData[method.value + 'Amount'] || '')" 
                (input)="method.value !== 'wallet' ? onAmountInput($event, method.value + 'Amount') : null"
                [min]="0" 
                [max]="method.value === 'cash' && (!paymentData.onlineAmount || paymentData.onlineAmount <= 0) && (!paymentData.walletAmount || paymentData.walletAmount <= 0) ? undefined : (method.value === 'cash' ? totalAmount : (totalAmount - (paymentData.cashAmount || 0)))"
                [disabled]="isProcessing || method.disabled || (method.value !== 'cash' && (paymentData.cashAmount || 0) >= totalAmount) || (method.value === 'wallet')"
                class="w-full p-2 border rounded" 
                step="0.01" 
                autocomplete="off">  
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Order Summary -->
    <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
      <div class="grid grid-cols-1 gap-2 text-2xl">
        <div class="flex justify-between">
          <span class="font-bold text-gray-800">Total Amount:</span>
          <span class="font-bold">{{ totalAmount | currency:'INR' }}</span>
        </div>

        <div class="flex justify-between text-lg text-gray-600" *ngIf="remainingAmount > 0">
          <span>Remaining Amount to Pay</span>
        <span class="font-semibold blink  text-red-600 animate-pulse text-lg font-bold duration-300">
            {{ remainingAmount | currency:'INR' }}
          </span>
        </div>
        
        <div *ngIf="change > 0" class="flex justify-between text-lg text-green-600">
          <span> Return Amount:</span>
          <span>{{ change | currency:'INR' }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Buttons -->
  <ng-template pTemplate="footer">
    <div class="flex justify-end w-full gap-2 mt-2">
      <button pButton pRipple [severity]="'danger'" type="button" [label]="cancelButtonLabel" [disabled]="isProcessing"
        (click)="onCancel()"></button>
      <button pButton pRipple type="button" [label]="confirmButtonLabel"
        [disabled]="isProcessing" [loading]="isProcessing"
        (click)="onConfirm()"></button>
    </div>
  </ng-template>
</p-dialog>