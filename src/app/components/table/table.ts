import { Component, EventEmitter, Input, Output, OnChanges, SimpleChanges, ViewChild } from '@angular/core';
import { Table } from 'primeng/table';

@Component({
  selector: 'app-table',
  standalone: false,
  templateUrl: './table.html',
})
export class TableComponent implements OnChanges {
  @Input() loading = false;
  @Input() scrollable = true;
  @Input() pagination = true;
  @Input() scrollHeight: string = 'calc(100vh - 160px)';
  @Input() tableData: any[] = [];
  @Input() tableColumns: any[] = [];
  @Input() selectedRows: any[] = [];
  @Input() dataKey: string = '';
  @Input() exportCSV = true; 
  @Input() globalFilter = true;
  @Output() onChange = new EventEmitter<any>();
  @Output() onActionClick = new EventEmitter<any>();
  @Output() onRowClick = new EventEmitter<any>();
  @ViewChild('table') table!: Table;
  @Input() globalFilterFields: string[] = [];
  @Input() sortable = true;
  ngOnChanges(changes: SimpleChanges) {
    if (changes['tableData'] && this.tableData && this.tableData.length > 0) {
      this.tableData.forEach(product => {
        if (!product.quantity) {
          product.quantity = 1;
        }
      });
    }
    this.globalFilterFields = this.tableColumns.map((col: any) => col.field).filter((field: any) => field);
  }

  onInputChange(event: any, rowData: any, column: any) {
    this.onChange.emit({ event, rowData, column });
  }
  
  buttonClick(button: any, rowData: any) {
    this.onActionClick.emit({ button, rowData });
  }

  rowClick(event: any, rowData: any, column: any) {
    this.onRowClick.emit({ event, rowData, column });
  }

  getModel(rowData: any, col: any){
    return col.body ? col.body(rowData) : rowData[col.field];
  }

  filter(event: any) {
    this.table.filterGlobal(event?.target?.value || '', 'contains');
  }
}
