import { Component, EventEmitter, Input, Output, ViewChild} from '@angular/core';
import { FormGroupDirective} from '@angular/forms';
import { CommonService } from 'src/app/services/common.service';
@Component({
  selector: 'app-common-form',
  standalone: false,
  templateUrl: './common-form.component.html',
})
export class CommonFormComponent {
  protected readonly Number = Number;
  @Input() inputFields: any[] = [];
  @Input() mainClass = 'modal-search';
  @Input() designClass = '';
  @Input() appendTo: any;
  @Input() itemId: any;
  @Input() showFilter = false;
  @Input() classList = 'p-2 col-12 md:col-6 lg:col-3';
  @ViewChild('myForm') form: FormGroupDirective | any;
  @Output() sendForm: EventEmitter<any> = new EventEmitter();
  @Output() changeDetect: EventEmitter<any> = new EventEmitter();
  @Output() selection: EventEmitter<any> = new EventEmitter();
  @Output() iconEvent: EventEmitter<any> = new EventEmitter();
  @Output() removeSelected: EventEmitter<any> = new EventEmitter();
  @Input() formIndex = 0;
  @Input() permissions: any = {};
  @Input() enterKeyHint = '';
  filterdata = '';
  constructor(
      private common: CommonService,
  ) {
  }

  onSubmit(){
    if (this.form.valid){
      this.sendForm.emit(this.form);
    } else {
      for (const el in this.form['controls']) {
        if (this.form['controls'][el].errors) {
          this.form['controls'][el].markAsDirty();
        }
      }
      this.common.toast({ severity: 'error', summary: 'Error', detail: 'Please fill all mandatory fields.' });
      this.sendForm.emit(this.form);
    }
  }


  ngOnChanges(){
  }
  async getFormData(){
    return new Promise((resolve) => {
      if (this.form.valid){
        resolve(this.form)
      } else {
        for (const el in this.form['controls']) {
          if (this.form['controls'][el].errors) {
            this.form['controls'][el].markAsDirty();
          }
        }
        this.common.setDisableBtn();
        this.common.toast({ severity: 'error', summary: 'Error', detail: 'Please fill all mandatory fields.' });
      }
    })
  }
  checkChange(event: any, data: any, index: any, inputComponent: any = null){
    if (data.detectChanges && !data.debounce){
      this.changeDetect.emit({event, data, index, component: inputComponent});
    }
  }
  removeSelectedValue(inputType: any, selectedItem: any){
     this.removeSelected.emit({inputType, selectedItem});
  }
  checkDebounceChange(event: any, data: any, index: any){
    if (data.detectChanges){
      this.changeDetect.emit({event, data, index});
    }
  }
  resetForm(){
    if(this.form){
      this.form.resetForm();
    }
  }
  selected(event: any, data: any, index: any){
    this.selection.emit({event, data, index});
  }
  iconClick(input: any, btn: any = {}, component: any = null){
    this.iconEvent.emit({input, form_data: this.form.form.value, btn, component});
  }
  setFocus(input: any){
    setTimeout(() => {
      const element = document.getElementById(input);
      if (element) {
        element.focus()
      }
    }, 100)
  }
}
