<ion-header [translucent]="true">
    <ion-toolbar>
      <div class="flex items-center">
        <ion-buttons slot="start">
          <ion-menu-button autoHide="false"></ion-menu-button>
        </ion-buttons>
        <div class="flex items-center w-full justify-between pr-4">
          <span class="mr-2 flex items-center"><img src="https://djpw4cfh60y52.cloudfront.net/uploads/logo/ljD8TdmW3xUCdu1eHw8Gd31UIsMrYpt6DVHm3kRy.jpg" alt="Logo" width="120px"></span>
          <p-dropdown (onChange)="facilityChange($event)" appendTo="body" [options]="facilities" [(ngModel)]="facility" optionLabel="facilityName" placeholder="Select Facility"></p-dropdown>
        </div>
      </div>
    </ion-toolbar>
</ion-header>