import { HttpInterceptorFn, HttpRequest, HttpHandlerFn, HttpErrorResponse, HttpEvent, HttpResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { Observable, catchError, map, throwError, finalize, switchMap, from } from 'rxjs';
import { FirebaseAuthService } from '../services/firebase-auth.service';
import { environment } from '../../environments/environment';
/**
 * Response interceptor to format all API responses
 * Success: { success: true, data: response }
 * Failure: { success: false, data: response }
 */
export const responseInterceptor: HttpInterceptorFn = (
  req: HttpRequest<unknown>,
  next: HttpHandlerFn
): Observable<HttpEvent<unknown>> => {
  const firebaseAuthService = inject(FirebaseAuthService);
  
  return next(req).pipe(
    map(event => {
      if (event instanceof HttpResponse) {
        const formattedBody = {
          success: true,
          data: event.body
        };
        
        const clonedEvent = event.clone({
          body: formattedBody
        });
        return clonedEvent;
      }
      
      return event;
    }),
    catchError((error: HttpErrorResponse) => {
      console.error(`Response Interceptor: Error ${error.status}:`, error);
      if (error.status === 401 && req.url?.includes(environment.authBaseUrl)){
        firebaseAuthService.signOut();
        return throwError(() => error);
      }
      if (error.status === 401 || error.status === 0) {
        console.log('Response Interceptor: Token expired, attempting to refresh...');
        
        return from(firebaseAuthService.refreshToken())
          .pipe(
            switchMap(newToken => {
              if (newToken) {
                console.log('Response Interceptor: Token refreshed successfully');
                
                const authReq = req.clone({
                  setHeaders: {
                    Authorization: `${newToken}`,
                    'Content-Type': 'application/json'
                  }
                });
                
                console.log('Response Interceptor: Retrying request with new token');
                return next(authReq);
              } else {
                console.error('Response Interceptor: Token refresh failed - no new token');
                return throwError(() => error);
              }
            }),
            catchError(refreshError => {
              console.error('Response Interceptor: Token refresh failed:', refreshError);
              
              const formattedError = new HttpErrorResponse({
                error: {
                  success: false,
                  data: error.error
                },
                headers: error.headers,
                status: error.status,
                statusText: error.statusText,
                url: error.url || undefined
              });
              
              return throwError(() => formattedError);
            })
          );
      } else {
        const formattedError = new HttpErrorResponse({
          error: {
            success: false,
            data: error.error
          },
          headers: error.headers,
          status: error.status,
          statusText: error.statusText,
          url: error.url || undefined
        });
        
        return throwError(() => formattedError);
      }
    }),
    finalize(() => {
      console.log(`Response Interceptor: Request to ${req.url} completed`);
    })
  );
};
