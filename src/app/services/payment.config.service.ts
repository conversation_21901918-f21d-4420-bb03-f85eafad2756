import { Injectable } from '@angular/core';
import { MessageService } from 'primeng/api';

export interface PaymentMethodField {
  key: string;
  label: string;
  type: 'amount' | 'text' | 'radio' | 'select';
  required?: boolean;
  min?: number;
  max?: number;
  placeholder?: string;
  options?: { label: string; value: any }[];
}

export interface PaymentMethod {
  value: string;
  label: string;
  icon: string;
  color: string;
  fields: PaymentMethodField[];
  disabled?: boolean;
}

export interface PaymentData {
  selectedPaymentMethod: string;
  cashAmount?: number;
  onlineAmount?: number;
  walletAmount?: number;
  [key: string]: any;
}

export interface MultiPaymentData {
  cashAmount: number;
  onlineAmount: number;
  walletAmount?: number;
}

export interface PaymentModalOutput {
  paymentMethod: string;
  paymentData: PaymentData;
  multiPaymentData?: MultiPaymentData;
  customerName: string;
  customerPhone: string;
  customerId: string;
}

@Injectable({ providedIn: 'root' })
export class PaymentService {
  private MAX_AMOUNT = 999999;
  private paymentMethods: PaymentMethod[] = [
    {value: 'cash', label: 'Cash', icon: 'pi pi-money-bill', color: 'green',
      fields: [{ key: 'cashAmount', label: 'Amount Received', type: 'amount', required: true, min: 0 }]
    },
    {
      value: 'online', label: 'Online', icon: 'pi pi-wallet', color: 'blue',
      fields: [
        { key: 'onlineAmount', label: 'Amount', type: 'amount', required: true, min: 1 },
      ]
    },
    {
      value: 'wallet', label: 'Wallet', icon: 'pi pi-credit-card', color: 'indigo',
      fields: [
        { key: 'walletAmount', label: 'Amount', type: 'amount', required: true, min: 1  },
      ]
    },
  ];

  constructor(private messageService: MessageService) { }

  getPaymentMethods(totalAmount: number): PaymentMethod[] {
    return this.paymentMethods.map(method => ({
      ...method,
      fields: method.fields.map(field => ({
        ...field,
        max: field.type === 'amount' ? Math.max(totalAmount * 2, this.MAX_AMOUNT) : field.max
      }))
    }));
  }

  getPaymentMethod(methodValue: string, totalAmount: number): PaymentMethod | undefined {
    return this.getPaymentMethods(totalAmount).find(m => m.value === methodValue);
  }

  initializePaymentData(totalAmount: number): PaymentData {
    return {
      selectedPaymentMethod: 'cash',
      cashAmount: totalAmount
    };
  }

  validatePayment(paymentData: PaymentData, customerName: string, customerPhone: string, totalAmount: number, isSecondStep: boolean = false): boolean {
    if (!customerName?.trim()) return this.showError('Customer name is required');
    if (!customerPhone?.trim()) return this.showError('Customer phone is required');
    if (customerPhone?.trim().length !== 10) {
      return this.showError('Phone number should be exact 10 digits');
    }

    // Validate payment method
    const method = this.getPaymentMethod(paymentData.selectedPaymentMethod, totalAmount);
    if (!method) return this.showError('Invalid payment method');

    // Check if any payment amount exceeds the total
    const cashAmount = paymentData.cashAmount || 0;
    const onlineAmount = paymentData.onlineAmount || 0;
    const walletAmount = paymentData.walletAmount || 0;
    const totalPaid = cashAmount + onlineAmount + walletAmount;

    // If using multiple payment methods, validate the amounts
    if (cashAmount > 0 && (onlineAmount > 0 || walletAmount > 0)) {
      // When using multiple payment methods, cash cannot exceed the remaining amount
      if (cashAmount > totalAmount) {
        return this.showError('Cash amount cannot exceed the total amount when using multiple payment methods');
      }
      if (totalPaid < totalAmount) {
        return this.showError(`Total paid amount (${totalPaid}) is less than the order total (${totalAmount})`);
      }
    } 
    // If only using cash, allow it to exceed the total (for giving change)
    else if (cashAmount > 0) {
      // No need to check upper limit for cash-only payments
      if (cashAmount < totalAmount) {
        return this.showError(`Cash amount (${cashAmount}) is less than the order total (${totalAmount})`);
      }
    }
    // For online or wallet payments, they should exactly match the total
    else if ((onlineAmount > 0 || walletAmount > 0) && totalPaid !== totalAmount) {
      return this.showError(`Total paid amount (${totalPaid}) must exactly match the order total (${totalAmount})`);
    }

    return true;
  }

  calculateAmounts(paymentData: PaymentData, totalAmount: number): { remaining: number; change: number; isValid: boolean } {
    const amountKeys = ['cashAmount', 'onlineAmount', 'walletAmount'];
    let paidAmount = 0;
    
    // Calculate total from all payment methods
    amountKeys.forEach(key => {
      if (paymentData[key]) {
        paidAmount += parseFloat(paymentData[key] as string) || 0;
      }
    });

    // Check if the total paid matches the total amount (with a small tolerance for floating point)
    const tolerance = 0.01;
    const isTotalValid = Math.abs(paidAmount - totalAmount) < tolerance;

    return {
      remaining: Math.max(0, totalAmount - paidAmount),
      change: Math.max(0, paidAmount - totalAmount),
      isValid: isTotalValid
    };
  }

  private showError(message: string): boolean {
    this.messageService.add({ severity: 'error', summary: 'Error', detail: message });
    return false;
  }
}
