import { Injectable } from '@angular/core';
import { RecaptchaVerifier, signInWithPhoneNumber, signInWithCustomToken, User, getAuth } from 'firebase/auth';
import { BehaviorSubject } from 'rxjs';
import { auth } from '../firebase/firebase.config';
import { CommonService } from './common.service';
import { Router } from '@angular/router';
import { StorageService } from './storage.service';

@Injectable({
  providedIn: 'root'
})
export class FirebaseAuthService {
  private recaptchaVerifier: RecaptchaVerifier | null = null;
  private confirmationResult: any = null;
  private authStateSubject = new BehaviorSubject<any>(null);
  private tokenRefreshAttempts = 0;
  private maxRefreshAttempts = 3;
  private firebaseInitialized = false;
  
  authState$ = this.authStateSubject.asObservable();

  constructor(
    private commonService: CommonService,
    private router: Router,
    private storage: StorageService
  ) {
    // Check if Firebase auth is initialized
    try {
      auth.onAuthStateChanged((user) => {
        this.firebaseInitialized = true;
        this.authStateSubject.next(user);
      });
    } catch (error) {
      console.error('Error initializing Firebase Auth:', error);
    }
  }

  initRecaptcha(containerId: string) {
    if (!this.recaptchaVerifier) {
      this.recaptchaVerifier = new RecaptchaVerifier(auth, containerId, {
        size: 'invisible',
        callback: () => {
          // reCAPTCHA solved, allow signInWithPhoneNumber
        },
        'expired-callback': () => {
          // Response expired. Ask user to solve reCAPTCHA again
        }
      });
    }
    return this.recaptchaVerifier;
  }

  async sendOTP(phoneNumber: string, recaptchaContainerId: string): Promise<boolean> {
    try {
      const recaptchaVerifier = this.initRecaptcha(recaptchaContainerId);
      const formattedPhoneNumber = phoneNumber.startsWith('+') ? phoneNumber : `+91${phoneNumber}`;
      this.confirmationResult = await signInWithPhoneNumber(auth, formattedPhoneNumber, recaptchaVerifier);
      return true;
    } catch (error) {
      console.error('Error sending OTP:', error);
      return false;
    }
  }

  async verifyOTP(otp: string): Promise<any> {
    try {
      if (!this.confirmationResult) {
        throw new Error('No confirmation result found. Please send OTP first.');
      }
      const result = await this.confirmationResult.confirm(otp);
      return {
        success: true,
        user: result.user
      };
    } catch (error) {
      console.error('Error verifying OTP:', error);
      return {
        success: false,
        error
      };
    }
  }

  async signOut(): Promise<boolean> {
    try {
      await auth.signOut();
      this.authStateSubject.next(null);
      this.storage.clear();
      this.commonService.toast({severity: 'success', summary: 'Success', detail: 'Logout successful!'});
      this.router.navigate(['/login']);
      return true;
    } catch (error) {
      console.error('Error signing out:', error);
      return false;
    }
  }

  /**
   * Get the current authenticated user with fallback to stored user data
   * @returns The current user or null if not authenticated
   */
  async getCurrentUser() {
    // First check Firebase auth state
    const firebaseUser = auth.currentUser;
    
    if (firebaseUser) {
      console.log('User found in Firebase auth state');
      return firebaseUser;
    }
    
    // If Firebase auth doesn't have a user, try to ensure Firebase is initialized
    await this.ensureFirebaseInitialized();
    
    // Check again after ensuring initialization
    if (auth.currentUser) {
      console.log('User found after ensuring Firebase initialization');
      return auth.currentUser;
    }
    
    // As a last resort, check if we have a token and try to restore the session
    const token = this.storage.getItem('token');
    const storedUser = this.storage.getItem('user');
    
    if (token && storedUser) {
      console.log('Attempting to restore session from stored credentials');
      try {
        // We can't directly use the stored token with signInWithCustomToken
        // as it requires a token generated by Firebase Admin SDK
        // Instead, let's just check if auth state has been restored
        const currentUser = auth.currentUser;
        if (!currentUser) {
          console.log('Attempting to refresh auth state');
          await this.refreshToken();
        }
        return auth.currentUser;
      } catch (error) {
        console.error('Failed to restore session:', error);
        // If token is invalid, clear stored data
        this.storage.clear();
      }
    }
    
    console.warn('No authenticated user found');
    return null;
  }
  
  /**
   * Checks if Firebase Auth is initialized and waits if necessary
   * @param maxWaitMs Maximum time to wait in milliseconds
   * @returns Promise that resolves when Firebase is initialized or times out
   */
  private async ensureFirebaseInitialized(maxWaitMs: number = 3000): Promise<boolean> {
    if (this.firebaseInitialized) {
      return true;
    }
    
    console.log('Waiting for Firebase Auth to initialize...');
    
    return new Promise<boolean>(resolve => {
      let waitTime = 0;
      const interval = 100; // Check every 100ms
      
      const checkInterval = setInterval(() => {
        waitTime += interval;
        
        if (this.firebaseInitialized) {
          clearInterval(checkInterval);
          console.log('Firebase Auth initialized successfully');
          resolve(true);
        } else if (waitTime >= maxWaitMs) {
          clearInterval(checkInterval);
          console.warn(`Firebase Auth initialization timed out after ${maxWaitMs}ms`);
          resolve(false);
        }
      }, interval);
    });
  }

  /**
   * Attempt to refresh the Firebase token with retry mechanism and auth state recovery
   * @returns Promise that resolves to the new token or null if refresh failed
   */
  async refreshToken(): Promise<string | null> {
    try {
      await this.ensureFirebaseInitialized();
      
      // Check if we've exceeded max retry attempts
      if (this.tokenRefreshAttempts >= this.maxRefreshAttempts) {
        console.error(`Token refresh failed after ${this.maxRefreshAttempts} attempts. Redirecting to login.`);
        this.tokenRefreshAttempts = 0; // Reset counter
        this.signOut(); // Force sign out
        this.router.navigate(['/login']);
        return null;
      }
      
      this.tokenRefreshAttempts++;
      console.log(`Token refresh attempt ${this.tokenRefreshAttempts} of ${this.maxRefreshAttempts}`);
      
      const user = auth.currentUser;
      if (user) {
        console.log('Current user found, refreshing token directly');
        try {
          const newToken = await user.getIdToken(true);
          console.log('Token refreshed successfully');
          this.storage.setItem('token', newToken);
          this.tokenRefreshAttempts = 0; // Reset counter on success
          return newToken;
        } catch (tokenError) {
          console.error('Error getting new token:', tokenError);
          return null;
        }
      } else {
        console.log('No current user available for token refresh');
        return null;
      }
    } catch (error) {
      console.error('Error in refreshToken process:', error);
      return null;
    }
  }
}
