import { Injectable } from "@angular/core";
import { CommonService } from "./common.service";
import { lastValueFrom } from "rxjs";
@Injectable({
    providedIn: 'root'
})
export class CustomerService {
    constructor(
        private commonService: CommonService,
    ) { }
   async verifyCustomer(phoneNumber:number): Promise<any>{
    try {
      const response = await lastValueFrom(this.commonService.get<any>(`api/customer`, {phone_number: `+91${phoneNumber}`}, {authBaseUrl: true}));
      return response;
    } catch (error: any) {
      return error;
    }
   }

   async createCustomer(customerData:any): Promise<any>{
    try {
      const response = await lastValueFrom(this.commonService.post<any>(`api/customer`, customerData, {authBaseUrl: true}));
      return response;
    } catch (error: any) {
      return error
    }
   }
}