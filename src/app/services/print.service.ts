import { Injectable } from '@angular/core';
import { PrintTemplateUtil } from '../utils/print-template.util';
import { TaxCalculationUtils } from '../utils/tax-calculation.utils';
import { PrintTemplateData, PrintableItem, TaxConfig } from '../models/print.model';
import { Order, CartItem } from '../models';
import { TypeSenseService } from './typesense.service';

interface StoreInfo {
  address: string;
  phone: string;
  gstin: string;
  name: string;
  email: string;
}

@Injectable({
  providedIn: 'root'
})
export class PrintService {
 static DEFAULT_STORE_INFO: StoreInfo = {
    address: 'New Delhi, India',
    phone: '9667018020',
    gstin: '27AAPC1234D1Z1',
    name: 'ROZANA RURAL COMMERCE PVT LTD',
    email: '<EMAIL>'
  };

  private storeInfoCache: StoreInfo | null = null;

  constructor(private typeSenseService: TypeSenseService) { }

  clearStoreCache(): void {
    this.storeInfoCache = null;
  }

  private async getStoreInfo(): Promise<StoreInfo> {
    if (this.storeInfoCache) return this.storeInfoCache;

    try {
      const storeData = await this.typeSenseService.getStoreById();
      return this.storeInfoCache = { ...PrintService.DEFAULT_STORE_INFO, ...storeData };
    } catch (error) {
      console.error('Error fetching store info, using defaults:', error);
      return this.storeInfoCache = { ...PrintService.DEFAULT_STORE_INFO };
    }
  }




  private convertToItem(item: CartItem | any): PrintableItem {
    return TaxCalculationUtils.convertCartItemToPrintableItem(item);
  }

  private async generateTemplate(items: PrintableItem[], orderData: any): Promise<string> {
    const totals = TaxCalculationUtils.calculatePrintableTotals(items);
    const storeInfo = await this.getStoreInfo();
    
    const calculatedTotal = totals.totalTaxableAmount + totals.totalTaxAmount;

    const templateData: PrintTemplateData = {
      order_id: orderData.order_id,
      customer_name: orderData.customer_name || '',
      customer_id: orderData.customer_id || '',
      facility_name: orderData.facility_name || storeInfo.name,
      total_amount: orderData.total_amount || calculatedTotal,
      items: totals.processedItems || items,
      payment_method: orderData.payment_method || 'Cash',
      subtotal: orderData.subtotal_amount || totals.totalTaxableAmount,
      discount: orderData.discount_amount || items.reduce((total, item) => total + (item.discount || 0) * item.quantity, 0),
      grand_total: orderData.total_amount || calculatedTotal,
      copy_of_invoice: orderData.copy_of_invoice || false,
      currentDate: (orderData.order_date ? new Date(orderData.order_date) : new Date()).toLocaleDateString('en-IN'),
      totals,
      storeAddress: storeInfo.address,
      storePhone: storeInfo.phone,
      storeGSTIN: storeInfo.gstin,
      storeEmail: storeInfo.email,
      created_at: orderData.created_at?.toString()
    };

    return PrintTemplateUtil.generateTemplate(templateData);
  }

  async generateCartPrintTemplate(printData: any): Promise<string> {
    const items = printData.cartItems.map((item: any) => this.convertToItem(item));
    const orderData = {
      order_id: printData.orderId,
      customer_name: printData.customerName,
      payment_method: printData.paymentMethod,
      copy_of_invoice: printData.copyOfInvoice
    };
    return this.generateTemplate(items, orderData);
  }

  async generateOrderPrintTemplate(order: Order, copy_of_invoice = false): Promise<string> {
    const items = order.items.map(item => this.convertToItem(item));
    return this.generateTemplate(items, { ...order, copy_of_invoice });
  }

  private printTemplate(htmlTemplate: string): void {
    try {
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        console.error('Failed to open print window. Please check popup blocker settings.');
        return;
      }

      printWindow.document.open();
      printWindow.document.write(htmlTemplate);
      printWindow.document.close();
      printWindow.focus();
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    } catch (error) {
      console.error('Error printing template:', error);
    }
  }

  async printCart(printData: any): Promise<void> {
    const template = await this.generateCartPrintTemplate(printData);
    this.printTemplate(template);
  }

  async printOrder(order: Order, copy_of_invoice = false): Promise<void> {
    const template = await this.generateOrderPrintTemplate(order, copy_of_invoice);
    this.printTemplate(template);
  }
}