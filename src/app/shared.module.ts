import { NgModule } from "@angular/core";
import { DisableDoubleClickDirective } from "./directives/disable-double-click/disable-double-click.directive";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";
import { TableModule } from "primeng/table";
import { InputNumberModule } from "primeng/inputnumber";
import { ImageModule } from "primeng/image";
import { SkeletonModule } from "primeng/skeleton";
import { TooltipModule } from "primeng/tooltip";
import { DialogModule } from "primeng/dialog";
import { CalendarModule } from "primeng/calendar";
import { DropdownModule } from "primeng/dropdown";
import { AutoCompleteModule } from "primeng/autocomplete";
import { FormsModule } from "@angular/forms";
import { NoDataComponent } from "./components/no-data/no-data";
import { SkeletonComponent } from "./components/skeleton/skeleton.component";
import { DividerModule } from "primeng/divider";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { PaymentModalComponent } from "./components/payment-modal/payment-modal";
import { RadioButtonModule } from "primeng/radiobutton";
import { FloatLabelModule } from "primeng/floatlabel";
import { CommonModule } from "@angular/common";
import { TableComponent } from "./components/table/table";
import { CommonFormComponent } from "./components/common-form/common-form.component";
import { IonicModule } from "@ionic/angular";
import { CheckboxModule } from "primeng/checkbox";
import { MultiSelectModule } from "primeng/multiselect";
import { InputSwitchModule } from "primeng/inputswitch";
import { IftaLabelModule } from "primeng/iftalabel";
import { TabsModule } from "primeng/tabs";
import { BillingComponent } from "./components/billing/billing";

@NgModule({
    declarations: [
        DisableDoubleClickDirective,
        NoDataComponent,
        SkeletonComponent,
        PaymentModalComponent,
        TableComponent,
        CommonFormComponent,
        BillingComponent
    ],
    imports: [
        CommonModule,
        InputTextModule,
        ButtonModule,
        TableModule,
        InputNumberModule,
        ImageModule,
        SkeletonModule,
        TooltipModule,
        DialogModule,
        CalendarModule,
        DropdownModule,
        AutoCompleteModule,
        FormsModule,
        DividerModule,
        ProgressSpinnerModule,
        RadioButtonModule,
        FloatLabelModule,
        IonicModule,
        CheckboxModule,
        MultiSelectModule,
        InputSwitchModule,
        IftaLabelModule,
        TabsModule
    ],
    exports: [
        CommonModule,
        DisableDoubleClickDirective,
        InputTextModule,
        ButtonModule,
        TableModule,
        InputNumberModule,
        ImageModule,
        SkeletonModule,
        TooltipModule,
        DialogModule,
        CalendarModule,
        DropdownModule,
        AutoCompleteModule,
        FormsModule,
        NoDataComponent,
        SkeletonComponent,
        DividerModule,
        ProgressSpinnerModule,
        RadioButtonModule,
        FloatLabelModule,
        PaymentModalComponent,
        TableComponent,
        IonicModule,
        CheckboxModule,
        MultiSelectModule,
        InputSwitchModule,
        CommonFormComponent,
        BillingComponent,
        IftaLabelModule,
        TabsModule
    ]
})
export class SharedModule { }