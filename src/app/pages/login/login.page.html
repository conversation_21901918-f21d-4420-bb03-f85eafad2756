<ion-content [fullscreen]="true">
    <div class="min-h-screen  flex items-center justify-center p-4">
        <div
            class="bg-white shadow-xl rounded-3xl overflow-hidden max-w-5xl w-full  h-[450px] lg:h-[500px] flex flex-col lg:flex-row">
            <div
                class="hidden lg:flex lg:w-1/2 bg-gradient-to-l from-blue-900 to-blue-600 flex-col justify-center items-center p-12 text-white relative">
                <div class="relative z-10 text-center">
                    <div class="flex justify-center w-full mb-8">
                        <div class="bg-white rounded-lg p-4 shadow-lg">
                            <img class="w-32 h-auto" src="https://djpw4cfh60y52.cloudfront.net/uploads/logo/ljD8TdmW3xUCdu1eHw8Gd31UIsMrYpt6DVHm3kRy.jpg" alt="Logo" />
                        </div>
                    </div>
                    <h1 class="text-4xl font-bold mb-4 leading-tight">Welcome Back!</h1>
                    <p class="text-xl text-blue-100 leading-relaxed">Sign in to access your POS dashboard</p>
                </div>
            </div>
            
            <div class="w-full lg:w-1/2 p-8 lg:p-12 flex  m-auto  items-center justify-center ">
                <div class="w-full max-w-md">
                    <div class="lg:hidden flex justify-center mb-8">
                        <img class="w-36 h-auto" src="https://djpw4cfh60y52.cloudfront.net/uploads/logo/ljD8TdmW3xUCdu1eHw8Gd31UIsMrYpt6DVHm3kRy.jpg" alt="Logo" />
                    </div>

                    <div class="text-center mb-8">
                        <h1 class="text-3xl font-bold mb-4 leading-tight">Welcome Back!</h1>
                        <p class="text-md text-gray-600 leading-relaxed">Sign in to access your POS dashboard</p>
                    </div>

                    <!-- reCAPTCHA container -->
                    <div id="recaptcha-container"></div>

                    <form class="space-y-6" (ngSubmit)="onLogin()" *ngIf="!showOtpInput">
                        <div class="space-y-1">
                            <p-ifta-label>
                                <p-inputNumber mode="decimal" inputId="withoutgrouping" [useGrouping]="false" [minlength]="10" [maxlength]="10" class="w-full h-[4rem]" id="phoneNumber" placeholder="Enter your phone number" [(ngModel)]="phoneNumber" name="phoneNumber" />
                                <label for="phoneNumber">Phone Number</label>
                            </p-ifta-label>
                        </div>

                        <button type="submit" pButton [loading]="isLoading" label="Send OTP" class="w-full h-[3rem]"></button>
                    </form>

                    <!-- OTP Verification Form -->
                    <form class="space-y-6" (ngSubmit)="verifyOtp()" *ngIf="showOtpInput">
                        <div class="space-y-1">
                            <p-ifta-label>
                                <input pInputText class="w-full h-[4rem]" id="otpCode" placeholder="Enter OTP sent to your phone" [(ngModel)]="otpCode" name="otpCode" />
                                <label for="otpCode">OTP Code</label>
                            </p-ifta-label>
                        </div>

                        <div class="flex flex-col space-y-3">
                            <button type="submit" pButton [loading]="isVerifying" label="Verify OTP" class="w-full h-[3rem]"></button>
                            <button type="button" pButton pRipple label="Resend OTP" (click)="resendOtp()" class="w-full h-[3rem] p-button-outlined"></button>
                            <button type="button" pButton pRipple label="Change Phone Number" (click)="changePhoneNumber()" class="w-full h-[3rem] p-button-text"></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</ion-content>