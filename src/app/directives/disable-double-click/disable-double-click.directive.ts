import {Directive, HostListener, Input, OnInit} from '@angular/core';
import { CommonService } from 'src/app/services/common.service';

@Directive({
  selector: '[appDisableDoubleClick]',
  standalone: false,
})
export class DisableDoubleClickDirective implements OnInit{
    @Input() appDisableDoubleClick = true;
    @Input() reloadIconOnly = false;
    @Input() noReloadIcon = false;
    private button: HTMLButtonElement | any;
    btnClicked = false;
    innerHtml = '';
    constructor(
        public common: CommonService,
    ) {
    }
    @HostListener('click', ['$event']) onClick(event: any){
        if (this.appDisableDoubleClick){
          event.preventDefault();
          event.stopPropagation();
          this.button = event.currentTarget;
          this.innerHtml = this.button.innerHTML.slice();
          const innerSpan = '<span class="p-button-icon p-button-icon-right pi pi-spin pi-spinner" aria-hidden="true"></span>';
          const html = this.noReloadIcon ? this.innerHtml : this.innerHtml + innerSpan;
          this.button.innerHTML = this.reloadIconOnly ? innerSpan : html;
          this.button.disabled = true;
          this.btnClicked = true;
        }
    }
    ngOnInit(){
        if (this.appDisableDoubleClick){
          this.common.disableBtn.subscribe(res => {
            if (this.btnClicked && !res){
              this.button.disabled = false;
              this.btnClicked = false;
              this.button.innerHTML = this.innerHtml;
            }
          });
        }
    }
}
